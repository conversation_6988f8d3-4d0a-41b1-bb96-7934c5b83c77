"""
UI components module for TeamLogic-AutoTask application.
Contains Streamlit UI components, styling, and utility functions.
"""

import streamlit as st
import json
import os
import pandas as pd
import plotly.express as px
from datetime import datetime, timedelta, date
from collections import Counter
from typing import List, Dict

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from config import *
from src.data import DataManager

# Import requests for API calls (with fallback)
try:
    import requests
except ImportError:
    requests = None


def apply_custom_css():
    """Apply custom dark theme CSS styling."""
    st.markdown("""
    <style>
    :root {
        --primary: #4e73df;
        --primary-hover: #2e59d9;
        --secondary: #181818;
        --accent: #23272f;
        --text-main: #f8f9fa;
        --text-secondary: #b0b3b8;
        --card-bg: #23272f;
        --sidebar-bg: #111111;
    }
    .main {
        background-color: var(--secondary);
        color: var(--text-main);
    }
    body, .stApp, .main, .block-container {
        background-color: var(--secondary) !important;
        color: var(--text-main) !important;
    }
    .stTextInput input, .stTextArea textarea,
    .stSelectbox select, .stDateInput input {
        background-color: #23272f !important;
        color: var(--text-main) !important;
        border: 1px solid #444 !important;
        border-radius: 6px !important;
    }
    .stTextInput input::placeholder, .stTextArea textarea::placeholder,
    .stSelectbox select:invalid, .stDateInput input::placeholder {
        color: var(--text-secondary) !important;
    }
    .stButton>button {
        background-color: var(--primary) !important;
        color: white !important;
        border: none;
        padding: 10px 24px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    .stButton>button:hover {
        background-color: var(--primary-hover) !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }
    .stMarkdown h1, .stMarkdown h2, .stMarkdown h3 {
        color: var(--primary);
    }
    .stSuccess {
        background-color: #1e4620 !important;
        color: #d4edda !important;
        border-radius: 8px;
    }
    .card {
        background-color: var(--card-bg);
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.15);
        margin-bottom: 20px;
        color: var(--text-main);
    }
    .sidebar .sidebar-content, .stSidebar, section[data-testid="stSidebar"] {
        background-color: var(--sidebar-bg) !important;
        color: var(--text-main) !important;
    }
    .stMetric {
        color: var(--text-main) !important;
    }
    .stExpanderHeader {
        color: var(--text-main) !important;
        background-color: var(--card-bg) !important;
    }
    .stExpanderContent {
        background-color: var(--card-bg) !important;
        color: var(--text-main) !important;
    }
    .stAlert, .stInfo, .stWarning {
        background-color: #23272f !important;
        color: #f8f9fa !important;
        border-radius: 8px;
    }
    @media (max-width: 768px) {
        .stForm {
            padding: 15px;
        }
    }
    </style>
    """, unsafe_allow_html=True)


def create_sidebar(data_manager: DataManager):
    """Create the navigation sidebar."""
    with st.sidebar:
        st.markdown("## Navigation")

        # Simple navigation buttons
        if st.button("🏠 Home", key="nav_main", use_container_width=True):
            st.session_state.page = "main"
            st.rerun()

        if st.button("🕒 Recent Tickets", key="nav_recent_tickets", use_container_width=True):
            st.session_state.page = "recent_tickets"
            st.rerun()

        if st.button("📊 Dashboard", key="nav_dashboard", use_container_width=True):
            st.session_state.page = "dashboard"
            st.rerun()

        # Show current page
        current_page = st.session_state.get('page', 'main')
        st.markdown(f"**Current:** {current_page.replace('_', ' ').title()}")

        st.markdown("---")

        # Quick Stats
        st.markdown("### Quick Stats")
        try:
            if os.path.exists(KNOWLEDGEBASE_FILE):
                with open(KNOWLEDGEBASE_FILE, 'r') as f:
                    kb_data = json.load(f)
                total_tickets = len(kb_data)
            else:
                total_tickets = 0
        except:
            total_tickets = 0
        st.metric("Total Tickets", total_tickets)

        # Contact Information
        st.markdown("---")
        st.markdown("### Need Help?")
        st.markdown(f"📞 **Phone:** {SUPPORT_PHONE}")
        st.markdown(f"✉️ **Email:** {SUPPORT_EMAIL}")


def format_time_elapsed(created_at):
    """Calculate and format time elapsed"""
    try:
        if isinstance(created_at, str):
            ticket_time = datetime.fromisoformat(created_at)
        else:
            ticket_time = created_at

        now = datetime.now()
        diff = now - ticket_time

        if diff.days > 0:
            return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours > 1 else ''} ago"
        else:
            minutes = max(1, diff.seconds // 60)
            return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
    except:
        return "Unknown"


def format_date_display(created_at):
    """Format date for display"""
    try:
        if isinstance(created_at, str):
            ticket_time = datetime.fromisoformat(created_at)
        else:
            ticket_time = created_at
        return ticket_time.strftime("%Y-%m-%d %H:%M:%S")
    except:
        return "Unknown"


def get_duration_icon(duration: str) -> str:
    """Get appropriate icon for duration"""
    return DURATION_ICONS.get(duration, "📅")


def create_metric_card(title: str, value: str, delta: str = None, delta_color: str = "normal"):
    """
    Create a metric card component.

    Args:
        title (str): Metric title
        value (str): Metric value
        delta (str, optional): Change indicator
        delta_color (str): Color for delta (normal, inverse, off)
    """
    st.metric(label=title, value=value, delta=delta, delta_color=delta_color)


def create_status_badge(status: str) -> str:
    """
    Create a colored status badge.

    Args:
        status (str): Status text

    Returns:
        str: HTML for colored status badge
    """
    color_map = {
        "Open": "#4e73df",
        "In Progress": "#f6c23e",
        "Resolved": "#36b9cc",
        "Closed": "#e74a3b",
        "New": "#4e73df",
        "Assigned": "#f6c23e",
        "Pending": "#fd7e14",
        "Available": "#28a745",
        "Busy": "#dc3545",
        "On Break": "#ffc107"
    }

    color = color_map.get(status, "#6c757d")
    return f'<span style="background-color: {color}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">{status}</span>'


def create_data_table(data: list, columns: list = None):
    """
    Create a data table component.

    Args:
        data (list): List of dictionaries or DataFrame
        columns (list, optional): Column names to display
    """
    if not data:
        st.info("No data available")
        return

    df = pd.DataFrame(data)
    if columns:
        df = df[columns] if all(col in df.columns for col in columns) else df

    st.dataframe(df, use_container_width=True)


def create_chart_container(chart_type: str, data: dict, title: str = ""):
    """
    Create a chart container with the specified chart type.

    Args:
        chart_type (str): Type of chart (bar, line, pie, etc.)
        data (dict): Chart data
        title (str): Chart title
    """
    if title:
        st.subheader(title)

    if chart_type == "bar":
        fig = px.bar(x=list(data.keys()), y=list(data.values()))
        st.plotly_chart(fig, use_container_width=True)
    elif chart_type == "pie":
        fig = px.pie(values=list(data.values()), names=list(data.keys()))
        st.plotly_chart(fig, use_container_width=True)
    elif chart_type == "line":
        fig = px.line(x=list(data.keys()), y=list(data.values()))
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.bar_chart(data)


def create_filter_section():
    """
    Create a filter section for data filtering.

    Returns:
        dict: Filter values
    """
    with st.expander("🔍 Filters", expanded=False):
        col1, col2, col3 = st.columns(3)

        with col1:
            date_filter = st.date_input("Date Range", value=[])

        with col2:
            status_filter = st.multiselect(
                "Status",
                options=STATUS_OPTIONS,
                default=STATUS_OPTIONS
            )

        with col3:
            priority_filter = st.multiselect(
                "Priority",
                options=PRIORITY_OPTIONS,
                default=PRIORITY_OPTIONS
            )

    return {
        "date_range": date_filter,
        "status": status_filter,
        "priority": priority_filter
    }


def api_call(endpoint: str, method: str = "GET", data: dict = None):
    """
    Make an API call to the backend.

    Args:
        endpoint (str): API endpoint
        method (str): HTTP method
        data (dict, optional): Request data

    Returns:
        dict: API response
    """
    if requests is None:
        return {"error": "requests library not available. Please install: pip install requests"}

    base_url = "http://127.0.0.1:8000/api"
    url = f"{base_url}/{endpoint.lstrip('/')}"

    try:
        if method.upper() == "GET":
            response = requests.get(url)
        elif method.upper() == "POST":
            response = requests.post(url, json=data)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data)
        elif method.upper() == "DELETE":
            response = requests.delete(url)
        else:
            return {"error": f"Unsupported method: {method}"}

        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"API call failed with status {response.status_code}"}

    except requests.exceptions.ConnectionError:
        return {"error": "Could not connect to API. Make sure FastAPI backend is running."}
    except Exception as e:
        return {"error": f"API call failed: {str(e)}"}


def display_api_response(response: dict, success_message: str = "Operation successful"):
    """
    Display API response with appropriate styling.

    Args:
        response (dict): API response
        success_message (str): Message to show on success
    """
    if "error" in response:
        st.error(f"❌ {response['error']}")
    else:
        st.success(f"✅ {success_message}")
        if response and len(response) > 0:
            with st.expander("📋 Response Details", expanded=False):
                st.json(response)


def display_database_status():
    """
    Display database connection status in sidebar.
    """
    try:
        from src.ui.database_helpers import test_database_connection, get_database_stats

        with st.sidebar:
            with st.expander("🗄️ Database Status", expanded=False):
                if test_database_connection():
                    st.success("✅ Database Connected")

                    # Show database stats
                    try:
                        stats = get_database_stats()
                        st.metric("Total Tickets", stats.get('total_tickets', 0))
                        st.metric("Open Tickets", stats.get('open_tickets', 0))

                        if stats.get('priority_breakdown'):
                            st.write("**Priority Breakdown:**")
                            for priority, count in stats['priority_breakdown'].items():
                                st.write(f"• {priority}: {count}")

                    except Exception as e:
                        st.warning(f"Stats unavailable: {e}")

                else:
                    st.error("❌ Database Disconnected")
                    st.info("Using mock data mode")

    except Exception as e:
        st.error(f"Database status error: {e}")


def create_ticket_card(ticket: dict, show_actions: bool = False, technician_mode: bool = False):
    """
    Create a standardized ticket card component.

    Args:
        ticket (dict): Ticket data
        show_actions (bool): Whether to show action buttons
        technician_mode (bool): Whether to show technician-specific actions
    """
    with st.container():
        # Header with ticket ID and status
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            st.markdown(f"**🎫 {ticket.get('id', 'Unknown')}**")
            st.markdown(f"*{ticket.get('title', 'No title')[:50]}...*")

        with col2:
            status = ticket.get('status', 'Unknown')
            status_badge = create_status_badge(status)
            st.markdown(status_badge, unsafe_allow_html=True)

        with col3:
            priority = ticket.get('priority', 'Medium')
            priority_colors = {
                "Low": "🟢", "Medium": "🟡", "High": "🟠",
                "Critical": "🔴", "Desktop/User Down": "🚨"
            }
            st.markdown(f"{priority_colors.get(priority, '⚪')} {priority}")

        # Details
        st.markdown(f"**Description:** {ticket.get('description', 'No description')[:100]}...")

        col1, col2 = st.columns(2)
        with col1:
            st.markdown(f"**Requester:** {ticket.get('requester_name', 'Unknown')}")
            st.markdown(f"**Email:** {ticket.get('requester_email', 'Unknown')}")

        with col2:
            st.markdown(f"**Created:** {ticket.get('created_at', 'Unknown')}")
            if ticket.get('assigned_technician'):
                st.markdown(f"**Assigned:** {ticket['assigned_technician']}")

        # Actions
        if show_actions:
            st.markdown("---")
            if technician_mode:
                col1, col2, col3 = st.columns(3)

                with col1:
                    if st.button("📝 Add Note", key=f"note_{ticket['id']}"):
                        st.session_state[f"show_note_{ticket['id']}"] = True

                with col2:
                    if st.button("📞 Contact", key=f"contact_{ticket['id']}"):
                        st.info(f"Contacting {ticket.get('requester_email', 'Unknown')}")

                with col3:
                    new_status = st.selectbox(
                        "Status:",
                        ["Open", "In Progress", "Resolved", "Closed"],
                        index=0,
                        key=f"status_{ticket['id']}"
                    )
            else:
                # User mode actions
                if st.button("📧 Email Update", key=f"email_{ticket['id']}"):
                    st.info("Email update request sent!")


def display_ai_processing_status():
    """
    Display AI processing status and capabilities.
    """
    with st.expander("🤖 AI Processing Status", expanded=False):
        try:
            from src.database.connection_manager import get_connection_manager

            manager = get_connection_manager()
            if manager.is_connected():
                st.success("✅ AI Agent Ready")
                st.info("🧠 Cortex LLM Available")
                st.info("🔄 Auto-classification Enabled")
                st.info("🎯 Smart Assignment Active")
            else:
                st.warning("⚠️ AI Agent in Mock Mode")
                st.info("Database connection required for full AI features")

        except Exception as e:
            st.error(f"AI status error: {e}")


def create_dashboard_metrics(stats: dict):
    """
    Create dashboard metrics display.

    Args:
        stats (dict): Statistics data
    """
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        create_metric_card(
            "Total Tickets",
            str(stats.get('total_tickets', 0)),
            delta=None
        )

    with col2:
        create_metric_card(
            "Open Tickets",
            str(stats.get('open_tickets', 0)),
            delta=None
        )

    with col3:
        resolved_tickets = stats.get('status_breakdown', {}).get('Resolved', 0)
        create_metric_card(
            "Resolved",
            str(resolved_tickets),
            delta=None
        )

    with col4:
        critical_tickets = stats.get('priority_breakdown', {}).get('Critical', 0)
        create_metric_card(
            "Critical",
            str(critical_tickets),
            delta=None,
            delta_color="inverse" if critical_tickets > 0 else "normal"
        )