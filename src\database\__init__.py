"""
Database Package
Contains database connection and operations.
Now uses centralized connection management.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.database.snowflake_db import SnowflakeConnection
from src.database.connection_manager import (
    ConnectionManager,
    get_connection_manager,
    initialize_connections,
    get_snowflake_connection,
    execute_query,
    call_cortex_llm,
    close_connections
)

__all__ = [
    'SnowflakeConnection',
    'ConnectionManager',
    'get_connection_manager',
    'initialize_connections',
    'get_snowflake_connection',
    'execute_query',
    'call_cortex_llm',
    'close_connections'
]
