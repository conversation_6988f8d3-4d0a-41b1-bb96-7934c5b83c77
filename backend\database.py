"""
Backend database module - now uses centralized connection manager.
This module provides backward compatibility while delegating to the centralized connection manager.
"""

import sys
import os
from typing import List, Dict, Optional

# Add project root to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.database.connection_manager import (
    get_connection_manager,
    initialize_connections as init_connections,
    close_connections as close_conn,
    execute_query as exec_query
)

# Backward compatibility functions
def connect_snowflake():
    """
    Backward compatibility function for connecting to Snowflake.
    Now delegates to centralized connection manager.
    """
    return init_connections()

def close_snowflake():
    """
    Backward compatibility function for closing Snowflake connection.
    Now delegates to centralized connection manager.
    """
    close_conn()

def execute_query(query: str, params: Optional[tuple] = None) -> List[Dict]:
    """
    Backward compatibility function for executing queries.
    Now delegates to centralized connection manager.

    Args:
        query (str): SQL query to execute
        params (tuple, optional): Query parameters

    Returns:
        List[Dict]: Query results as list of dictionaries
    """
    print(f"DEBUG: Backend execute_query called with query: {query[:100]}...")
    print(f"DEBUG: Backend execute_query params: {params}")

    try:
        results = exec_query(query, params)
        print(f"DEBUG: Backend execute_query returning {len(results)} results")
        return results
    except Exception as e:
        print(f"ERROR: Backend execute_query failed: {e}")
        raise e