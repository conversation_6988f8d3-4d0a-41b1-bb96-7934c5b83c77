"""
Comprehensive test to check Streamlit integration with the entire codebase.
Tests database connections, agent integration, API calls, and data flow.
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

def test_streamlit_database_integration():
    """Test if Streamlit UI components can access the centralized database."""
    print("🔍 Testing Streamlit Database Integration...")
    
    try:
        # Test connection manager import
        from src.database.connection_manager import get_connection_manager, execute_query
        print("✅ Connection manager imports successful")
        
        # Test connection status
        manager = get_connection_manager()
        connection_info = manager.get_connection_info()
        print(f"📊 Connection Status: {connection_info}")
        
        # Test query execution
        try:
            results = execute_query("SELECT 1 as test_value")
            print(f"✅ Database query test successful: {results}")
        except Exception as e:
            print(f"⚠️ Database query failed (expected if no Snowflake): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database integration test failed: {e}")
        return False

def test_streamlit_agent_integration():
    """Test if Streamlit can access and use AI agents."""
    print("\n🔍 Testing Streamlit Agent Integration...")
    
    try:
        # Test agent imports
        from src.agents.intake_agent import IntakeClassificationAgent
        print("✅ Agent imports successful")
        
        # Test agent creation with centralized connections
        try:
            agent = IntakeClassificationAgent.create_with_centralized_connections()
            print("✅ Agent creation with centralized connections successful")
            
            # Test if agent has connection manager
            if hasattr(agent, 'db_connection') and hasattr(agent.db_connection, 'connection_manager'):
                print("✅ Agent has centralized connection manager")
            else:
                print("⚠️ Agent may not be using centralized connection manager")
                
        except Exception as e:
            print(f"⚠️ Agent creation failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent integration test failed: {e}")
        return False

def test_streamlit_ui_components():
    """Test if UI components can make API calls and access backend."""
    print("\n🔍 Testing Streamlit UI Components...")
    
    try:
        # Test UI component imports
        from src.ui.components import api_call, display_api_response
        print("✅ UI component imports successful")
        
        # Test API call function
        try:
            response = api_call("health", "GET")
            print(f"📊 API call test result: {response}")
            
            if "error" in response:
                if "Could not connect to API" in response["error"]:
                    print("⚠️ FastAPI backend not running - this is expected")
                else:
                    print(f"⚠️ API call error: {response['error']}")
            else:
                print("✅ API call successful")
                
        except Exception as e:
            print(f"⚠️ API call test failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ UI components test failed: {e}")
        return False

def test_streamlit_data_flow():
    """Test the data flow from UI to backend to database."""
    print("\n🔍 Testing Streamlit Data Flow...")
    
    try:
        # Check if User.py uses database connections
        with open('src/ui/Pages/User.py', 'r') as f:
            user_content = f.read()
        
        database_imports = [
            'connection_manager',
            'SnowflakeConnection',
            'execute_query',
            'IntakeClassificationAgent'
        ]
        
        found_imports = []
        for imp in database_imports:
            if imp in user_content:
                found_imports.append(imp)
        
        if found_imports:
            print(f"✅ Found database-related imports in User.py: {found_imports}")
        else:
            print("❌ No database integration found in User.py")
            print("   User.py is using session state instead of database")
        
        # Check if Technician.py uses database connections
        with open('src/ui/Pages/Technician.py', 'r') as f:
            tech_content = f.read()
        
        found_tech_imports = []
        for imp in database_imports:
            if imp in tech_content:
                found_tech_imports.append(imp)
        
        if found_tech_imports:
            print(f"✅ Found database-related imports in Technician.py: {found_tech_imports}")
        else:
            print("❌ No database integration found in Technician.py")
            print("   Technician.py is using mock data instead of database")
        
        # Check for agent integration in ticket submission
        if 'IntakeClassificationAgent' in user_content:
            print("✅ User.py integrates with AI agents")
        else:
            print("❌ User.py does NOT integrate with AI agents")
            print("   Ticket submission is not using AI classification")
        
        return len(found_imports) > 0 or len(found_tech_imports) > 0
        
    except Exception as e:
        print(f"❌ Data flow test failed: {e}")
        return False

def test_streamlit_ticket_workflow():
    """Test the complete ticket workflow integration."""
    print("\n🔍 Testing Streamlit Ticket Workflow...")
    
    try:
        # Check if ticket submission triggers agent workflow
        with open('src/ui/Pages/User.py', 'r') as f:
            content = f.read()
        
        workflow_indicators = [
            'save_user_ticket',
            'IntakeClassificationAgent',
            'ai_processor',
            'execute_query',
            'api_call'
        ]
        
        found_workflow = []
        for indicator in workflow_indicators:
            if indicator in content:
                found_workflow.append(indicator)
        
        print(f"📊 Workflow indicators found: {found_workflow}")
        
        # Check if tickets are saved to database or just session state
        if 'st.session_state' in content and 'execute_query' not in content:
            print("⚠️ Tickets are saved to session state, NOT database")
            print("   This means tickets are lost when session ends")
        elif 'execute_query' in content:
            print("✅ Tickets are saved to database")
        
        # Check if AI classification is actually called
        if 'IntakeClassificationAgent' in content and 'process_ticket' in content:
            print("✅ AI classification is integrated in ticket workflow")
        else:
            print("❌ AI classification is NOT integrated in ticket workflow")
            print("   Tickets show 'Pending Classification' instead of actual AI results")
        
        return len(found_workflow) > 2
        
    except Exception as e:
        print(f"❌ Ticket workflow test failed: {e}")
        return False

def main():
    """Run all integration tests."""
    print("🚀 Starting Streamlit Integration Tests\n")
    
    tests = [
        test_streamlit_database_integration,
        test_streamlit_agent_integration,
        test_streamlit_ui_components,
        test_streamlit_data_flow,
        test_streamlit_ticket_workflow
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print(f"\n📊 Integration Test Results: {passed}/{total} tests passed")
    
    if passed < total:
        print("\n⚠️ INTEGRATION ISSUES FOUND:")
        print("   - Streamlit UI is NOT fully integrated with the codebase")
        print("   - UI components use mock data instead of database")
        print("   - Ticket submission does NOT trigger AI agents")
        print("   - Data is stored in session state, not database")
        print("\n🔧 RECOMMENDATIONS:")
        print("   1. Update User.py to import and use connection_manager")
        print("   2. Update Technician.py to use database queries")
        print("   3. Integrate IntakeClassificationAgent in ticket submission")
        print("   4. Replace session state with database storage")
    else:
        print("🎉 All integration tests passed! Streamlit is fully integrated.")

if __name__ == "__main__":
    main()
