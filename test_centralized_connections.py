"""
Test script for centralized connection management system.
Verifies that Snowflake and Cortex LLM connections are properly shared
across FastAPI backend and Streamlit frontend components.
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

def test_connection_manager():
    """Test the centralized connection manager."""
    print("🔍 Testing Centralized Connection Manager...")
    
    try:
        from src.database.connection_manager import get_connection_manager, initialize_connections
        
        # Test singleton pattern
        manager1 = get_connection_manager()
        manager2 = get_connection_manager()
        
        if manager1 is manager2:
            print("✅ Singleton pattern working correctly")
        else:
            print("❌ Singleton pattern failed")
            return False
        
        # Test connection initialization
        print("🔌 Initializing connections...")
        success = initialize_connections()
        
        if success:
            print("✅ Connections initialized successfully")
        else:
            print("⚠️ Connection initialization failed - running in mock mode")
        
        # Test connection info
        connection_info = manager1.get_connection_info()
        print(f"📊 Connection Status: {connection_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ Connection manager test failed: {e}")
        return False

def test_backend_integration():
    """Test backend integration with centralized connections."""
    print("\n🔍 Testing Backend Integration...")
    
    try:
        from backend.database import execute_query
        
        # Test query execution
        print("📊 Testing query execution...")
        results = execute_query("SELECT 1 as test_value")
        
        if results:
            print("✅ Backend query execution working")
            print(f"📋 Query results: {results}")
        else:
            print("⚠️ Backend query returned empty results")
        
        return True
        
    except Exception as e:
        print(f"❌ Backend integration test failed: {e}")
        return False

def test_agent_integration():
    """Test agent integration with centralized connections."""
    print("\n🔍 Testing Agent Integration...")
    
    try:
        from src.agents.intake_agent import IntakeClassificationAgent
        
        # Test agent creation with centralized connections
        print("🤖 Creating IntakeClassificationAgent with centralized connections...")
        agent = IntakeClassificationAgent.create_with_centralized_connections()
        
        if agent:
            print("✅ Agent created successfully with centralized connections")
            
            # Test that agent uses the same connection manager
            from src.database.connection_manager import get_connection_manager
            manager = get_connection_manager()
            
            if agent.db_connection.connection_manager is manager:
                print("✅ Agent uses the same connection manager instance")
            else:
                print("⚠️ Agent may not be using the same connection manager")
            
        return True
        
    except Exception as e:
        print(f"❌ Agent integration test failed: {e}")
        return False

def test_ui_components():
    """Test UI components integration."""
    print("\n🔍 Testing UI Components Integration...")
    
    try:
        from src.ui.components import apply_custom_css
        from src.database.connection_manager import get_connection_manager
        
        # Test that UI components can access connection manager
        manager = get_connection_manager()
        connection_info = manager.get_connection_info()
        
        print("✅ UI components can access connection manager")
        print(f"📊 Connection info accessible: {connection_info['initialized']}")
        
        return True
        
    except Exception as e:
        print(f"❌ UI components test failed: {e}")
        return False

def test_cortex_llm_integration():
    """Test Cortex LLM integration."""
    print("\n🔍 Testing Cortex LLM Integration...")
    
    try:
        from src.database.connection_manager import call_cortex_llm
        
        # Test simple LLM call
        print("🧠 Testing Cortex LLM call...")
        response = call_cortex_llm(
            "Return a simple JSON object with a 'status' field set to 'working'",
            model='mixtral-8x7b',
            expect_json=True
        )
        
        if response:
            print("✅ Cortex LLM call successful")
            print(f"📋 LLM Response: {response}")
        else:
            print("⚠️ Cortex LLM call returned None (may be in mock mode)")
        
        return True
        
    except Exception as e:
        print(f"❌ Cortex LLM integration test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Centralized Connection Management Tests\n")
    
    tests = [
        test_connection_manager,
        test_backend_integration,
        test_agent_integration,
        test_ui_components,
        test_cortex_llm_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Centralized connection management is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    # Cleanup
    try:
        from src.database.connection_manager import close_connections
        close_connections()
        print("🔌 Connections closed successfully")
    except Exception as e:
        print(f"⚠️ Error closing connections: {e}")

if __name__ == "__main__":
    main()
