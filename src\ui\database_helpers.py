"""
Database helper functions for Streamlit UI components.
Provides unified interface for database operations across UI pages.
"""

import streamlit as st
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
import sys
import os

# Add project root to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.database.connection_manager import execute_query, call_cortex_llm, get_connection_manager
from src.agents.intake_agent import IntakeClassificationAgent
from config import *

def generate_ticket_number() -> str:
    """Generate a unique ticket number."""
    try:
        # Try to get the next ticket number from database
        query = "SELECT MAX(CAST(SUBSTR(TICKETNUMBER, 12) AS INTEGER)) as max_num FROM COMPANY_4130_DATA WHERE TICKETNUMBER LIKE 'T%.%'"
        results = execute_query(query)
        
        if results and results[0]['MAX_NUM']:
            next_num = results[0]['MAX_NUM'] + 1
        else:
            next_num = 1
            
        # Format: T20240714.0001
        today = datetime.now().strftime("%Y%m%d")
        return f"T{today}.{next_num:04d}"
        
    except Exception as e:
        st.error(f"Error generating ticket number: {e}")
        # Fallback to timestamp-based number
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"T{timestamp[:8]}.{timestamp[8:]}"

def save_ticket_to_database(ticket_data: Dict) -> bool:
    """
    Save a ticket to the database using centralized connection.
    
    Args:
        ticket_data (Dict): Ticket information
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Prepare the INSERT query
        query = """
        INSERT INTO COMPANY_4130_DATA (
            TICKETNUMBER, ISSUE_TYPE, SUB_ISSUE_TYPE, TICKET_CATEGORY,
            PRIORITY, DESCRIPTION, STATUS, DUE_DATE, CREATEDATE,
            REQUESTER_NAME, REQUESTER_EMAIL, COMPANY_ID, DEVICE_MODEL,
            OS_VERSION, ERROR_MESSAGE, ASSIGNED_TECHNICIAN
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            ticket_data.get('ticket_number'),
            ticket_data.get('issue_type', 'General'),
            ticket_data.get('sub_issue_type', 'Support Request'),
            ticket_data.get('ticket_category', 'Technical Support'),
            ticket_data.get('priority', 'Medium'),
            ticket_data.get('description'),
            ticket_data.get('status', 'Open'),
            ticket_data.get('due_date'),
            ticket_data.get('create_date', datetime.now().isoformat()),
            ticket_data.get('requester_name'),
            ticket_data.get('requester_email'),
            ticket_data.get('company_id'),
            ticket_data.get('device_model'),
            ticket_data.get('os_version'),
            ticket_data.get('error_message'),
            ticket_data.get('assigned_technician')
        )
        
        execute_query(query, params)
        return True
        
    except Exception as e:
        st.error(f"Error saving ticket to database: {e}")
        return False

def get_user_tickets(user_email: str) -> List[Dict]:
    """
    Get all tickets for a specific user from the database.
    
    Args:
        user_email (str): User's email address
        
    Returns:
        List[Dict]: List of user's tickets
    """
    try:
        query = """
        SELECT * FROM COMPANY_4130_DATA 
        WHERE REQUESTER_EMAIL = ? 
        ORDER BY CREATEDATE DESC
        """
        
        results = execute_query(query, (user_email,))
        return results if results else []
        
    except Exception as e:
        st.error(f"Error fetching user tickets: {e}")
        return []

def get_technician_tickets(technician_id: str = None) -> List[Dict]:
    """
    Get tickets assigned to a specific technician or all tickets.
    
    Args:
        technician_id (str, optional): Technician ID
        
    Returns:
        List[Dict]: List of tickets
    """
    try:
        if technician_id:
            query = """
            SELECT * FROM COMPANY_4130_DATA 
            WHERE ASSIGNED_TECHNICIAN = ? 
            ORDER BY CREATEDATE DESC
            """
            results = execute_query(query, (technician_id,))
        else:
            query = """
            SELECT * FROM COMPANY_4130_DATA 
            ORDER BY CREATEDATE DESC
            LIMIT 100
            """
            results = execute_query(query)
            
        return results if results else []
        
    except Exception as e:
        st.error(f"Error fetching technician tickets: {e}")
        return []

def update_ticket_status(ticket_number: str, new_status: str, technician_id: str = None) -> bool:
    """
    Update ticket status in the database.
    
    Args:
        ticket_number (str): Ticket number
        new_status (str): New status
        technician_id (str, optional): Technician updating the ticket
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if technician_id:
            query = """
            UPDATE COMPANY_4130_DATA 
            SET STATUS = ?, ASSIGNED_TECHNICIAN = ?
            WHERE TICKETNUMBER = ?
            """
            params = (new_status, technician_id, ticket_number)
        else:
            query = """
            UPDATE COMPANY_4130_DATA 
            SET STATUS = ?
            WHERE TICKETNUMBER = ?
            """
            params = (new_status, ticket_number)
            
        execute_query(query, params)
        return True
        
    except Exception as e:
        st.error(f"Error updating ticket status: {e}")
        return False

def process_ticket_with_ai(ticket_data: Dict) -> Dict:
    """
    Process a ticket using the AI agent for classification and assignment.
    
    Args:
        ticket_data (Dict): Ticket information
        
    Returns:
        Dict: Processed ticket with AI classifications
    """
    try:
        # Create AI agent with centralized connections
        agent = IntakeClassificationAgent.create_with_centralized_connections()
        
        # Process the ticket through the AI workflow
        processed_ticket = agent.process_ticket(
            description=ticket_data.get('description', ''),
            requester_email=ticket_data.get('requester_email', ''),
            company_id=ticket_data.get('company_id', ''),
            device_info=f"{ticket_data.get('device_model', '')} {ticket_data.get('os_version', '')}".strip()
        )
        
        # Merge AI results with original ticket data
        if processed_ticket:
            ticket_data.update({
                'issue_type': processed_ticket.get('issue_type', 'General'),
                'sub_issue_type': processed_ticket.get('sub_issue_type', 'Support Request'),
                'ticket_category': processed_ticket.get('ticket_category', 'Technical Support'),
                'priority': processed_ticket.get('priority', 'Medium'),
                'assigned_technician': processed_ticket.get('assigned_technician'),
                'due_date': processed_ticket.get('due_date'),
                'ai_classification': processed_ticket.get('classification_results', {}),
                'resolution_suggestions': processed_ticket.get('resolution_suggestions', [])
            })
        
        return ticket_data
        
    except Exception as e:
        st.error(f"Error processing ticket with AI: {e}")
        # Return original ticket data if AI processing fails
        return ticket_data

def get_database_stats() -> Dict:
    """
    Get database statistics for dashboard display.
    
    Returns:
        Dict: Database statistics
    """
    try:
        stats = {}
        
        # Total tickets
        query = "SELECT COUNT(*) as total FROM COMPANY_4130_DATA"
        result = execute_query(query)
        stats['total_tickets'] = result[0]['TOTAL'] if result else 0
        
        # Open tickets
        query = "SELECT COUNT(*) as open FROM COMPANY_4130_DATA WHERE STATUS = 'Open'"
        result = execute_query(query)
        stats['open_tickets'] = result[0]['OPEN'] if result else 0
        
        # Tickets by priority
        query = "SELECT PRIORITY, COUNT(*) as count FROM COMPANY_4130_DATA GROUP BY PRIORITY"
        result = execute_query(query)
        stats['priority_breakdown'] = {row['PRIORITY']: row['COUNT'] for row in result} if result else {}
        
        # Tickets by status
        query = "SELECT STATUS, COUNT(*) as count FROM COMPANY_4130_DATA GROUP BY STATUS"
        result = execute_query(query)
        stats['status_breakdown'] = {row['STATUS']: row['COUNT'] for row in result} if result else {}
        
        return stats
        
    except Exception as e:
        st.error(f"Error fetching database stats: {e}")
        return {
            'total_tickets': 0,
            'open_tickets': 0,
            'priority_breakdown': {},
            'status_breakdown': {}
        }

def test_database_connection() -> bool:
    """
    Test if database connection is working.
    
    Returns:
        bool: True if connected, False otherwise
    """
    try:
        manager = get_connection_manager()
        return manager.is_connected()
    except Exception:
        return False
