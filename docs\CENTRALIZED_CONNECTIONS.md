# Centralized Connection Management System

## 🎯 Overview

The TeamLogic AutoTask system now implements a centralized connection management system that eliminates duplicate Snowflake and Cortex LLM connections between the FastAPI backend and Streamlit frontend. This ensures efficient resource usage and consistent database access across the entire application.

## 🏗️ Architecture

### Before (Duplicate Connections)
```
FastAPI Backend ──── Snowflake Connection 1
                     Cortex LLM Connection 1

Streamlit Frontend ── Snowflake Connection 2
                      Cortex LLM Connection 2

AI Agents ────────── Snowflake Connection 3
                     Cortex LLM Connection 3
```

### After (Centralized Connections)
```
                    ┌─── FastAPI Backend
Connection Manager ─┼─── Streamlit Frontend
(Singleton)         ├─── AI Agents
                    └─── All Components
                         │
                         ├── Single Snowflake Connection
                         └── Single Cortex LLM Connection
```

## 📁 Key Components

### 1. Connection Manager (`src/database/connection_manager.py`)
- **Singleton Pattern**: Ensures only one instance exists
- **Thread-Safe**: Uses threading locks for concurrent access
- **Auto-Reconnection**: Automatically reconnects if connection is lost
- **Mock Data Fallback**: Provides mock data when Snowflake is unavailable

### 2. Updated Backend (`backend/main.py`)
- **Startup Event**: Initializes connections once during FastAPI startup
- **Health Checks**: Provides connection status endpoints
- **Graceful Shutdown**: Closes connections during application shutdown

### 3. Updated Frontend (`app.py`)
- **Connection Reuse**: Reuses existing connections from the manager
- **Status Display**: Shows connection status in sidebar
- **Error Handling**: Gracefully handles connection failures

### 4. Backward Compatibility (`src/database/snowflake_db.py`)
- **Wrapper Class**: Maintains existing API while using centralized connections
- **Agent Compatibility**: Works with existing agent implementations
- **Seamless Migration**: No changes required in agent code

## 🚀 Usage

### Starting the System

1. **Start FastAPI Backend** (initializes connections):
```bash
uvicorn backend.main:app --reload --app-dir .
```

2. **Start Streamlit Frontend** (reuses connections):
```bash
streamlit run app.py
```

### Creating Agents with Centralized Connections

```python
# New recommended way
from src.agents.intake_agent import IntakeClassificationAgent

agent = IntakeClassificationAgent.create_with_centralized_connections()

# Old way still works (parameters ignored)
agent = IntakeClassificationAgent(
    sf_account="", sf_user="", sf_password="",  # Ignored
    sf_warehouse="", sf_database="", sf_schema="",  # Ignored
    sf_role="", sf_passcode=""  # Ignored
)
```

### Direct Connection Manager Usage

```python
from src.database.connection_manager import (
    get_connection_manager,
    initialize_connections,
    execute_query,
    call_cortex_llm
)

# Initialize connections (usually done in backend startup)
initialize_connections()

# Execute queries
results = execute_query("SELECT * FROM tickets LIMIT 10")

# Call Cortex LLM
response = call_cortex_llm("Analyze this ticket", model='mixtral-8x7b')

# Get connection status
manager = get_connection_manager()
status = manager.get_connection_info()
```

## 🔧 Configuration

All connection parameters are loaded from environment variables via `config.py`:

```env
SF_ACCOUNT=your_account
SF_USER=your_username
SF_PASSWORD=your_password
SF_WAREHOUSE=your_warehouse
SF_DATABASE=your_database
SF_SCHEMA=your_schema
SF_ROLE=your_role
SF_PASSCODE=your_mfa_code
```

## 🔄 Workflow

### 1. Application Startup
```
FastAPI Startup → Initialize Connections → Store in Singleton Manager
```

### 2. Streamlit Startup
```
Streamlit Start → Import Connection Manager → Reuse Existing Connections
```

### 3. Email Ticket Processing
```
Email Received → Email Processor → Use Shared Connections → Trigger Agents
```

### 4. UI Ticket Processing
```
Streamlit Form → Use Shared Connections → Trigger Agents → Process Ticket
```

### 5. Agent Processing
```
All Agents → Access Same Connection Instances → Process Ticket → Update Database
```

## 🛡️ Error Handling

- **Connection Failures**: Automatic reconnection attempts
- **Mock Data Mode**: Fallback to mock data when Snowflake unavailable
- **Graceful Degradation**: System continues to function without database
- **Logging**: Comprehensive logging for debugging connection issues

## 📊 Health Monitoring

### API Endpoints
- `GET /health` - Overall system health
- `GET /api/tickets/health` - Tickets service health
- `GET /api/technicians/health` - Technicians service health

### Streamlit UI
- Connection status displayed in sidebar
- Real-time connection monitoring
- Error notifications for connection issues

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_centralized_connections.py
```

Tests verify:
- Singleton pattern implementation
- Connection sharing across components
- Backend integration
- Agent integration
- UI component access
- Cortex LLM functionality

## 🔍 Troubleshooting

### Common Issues

1. **Connection Not Initialized**
   - Ensure FastAPI backend starts first
   - Check environment variables are set
   - Verify Snowflake credentials

2. **Mock Data Mode**
   - Check Snowflake connection parameters
   - Verify network connectivity
   - Review SSL/certificate settings

3. **Import Errors**
   - Ensure all paths are correctly configured
   - Check Python path includes project root
   - Verify all dependencies are installed

### Debug Information

```python
from src.database.connection_manager import get_connection_manager

manager = get_connection_manager()
print(manager.get_connection_info())
```

## 📈 Benefits

- **Resource Efficiency**: Single connection instead of multiple
- **Consistency**: All components use identical connection instances
- **Reliability**: Centralized error handling and reconnection logic
- **Maintainability**: Single point of connection management
- **Scalability**: Better connection pooling and resource management

## 🔮 Future Enhancements

- Connection pooling for high-concurrency scenarios
- Connection metrics and monitoring
- Automatic failover to backup Snowflake instances
- Connection caching and optimization
- Advanced retry strategies with exponential backoff
