"""
Centralized Connection Manager for TeamLogic-AutoTask Application.
Implements singleton pattern for Snowflake and Cortex LLM connections.
Ensures single connection instance shared across FastAPI backend and Streamlit frontend.
"""

import snowflake.connector
import threading
import logging
import json
import re
from typing import Dict, List, Optional, Any
from datetime import datetime
import sys
import os

# Add project root to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from config import *

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ConnectionManager:
    """
    Singleton connection manager for Snowflake and Cortex LLM connections.
    Ensures single connection instance shared across the entire application.
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """Implement singleton pattern with thread safety."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(ConnectionManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize connection manager (called only once due to singleton)."""
        if self._initialized:
            return
            
        self.snowflake_conn = None
        self.connection_params = None
        self._connection_lock = threading.Lock()
        self._initialized = True
        
        logger.info("ConnectionManager singleton instance created")
    
    def initialize_connections(self) -> bool:
        """
        Initialize Snowflake connection using configuration from config.py.
        Should be called once during application startup.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        with self._connection_lock:
            if self.snowflake_conn is not None and not self.snowflake_conn.is_closed():
                logger.info("Snowflake connection already established")
                return True
            
            try:
                # Load connection parameters from config
                self.connection_params = {
                    'user': SF_USER,
                    'password': SF_PASSWORD,
                    'account': SF_ACCOUNT,
                    'warehouse': SF_WAREHOUSE,
                    'database': SF_DATABASE,
                    'schema': SF_SCHEMA,
                    'role': SF_ROLE,
                    'passcode': SF_PASSCODE,
                    # SSL configuration to handle certificate issues
                    'validate_default_parameters': False,
                    'disable_request_pooling': True,
                    'insecure_mode': True,  # Disable SSL certificate validation
                    'ocsp_fail_open': True,  # Allow connection even if OCSP check fails
                }
                
                # Remove None values
                self.connection_params = {k: v for k, v in self.connection_params.items() if v is not None}
                
                logger.info("Establishing Snowflake connection...")
                self.snowflake_conn = snowflake.connector.connect(**self.connection_params)
                
                # Test connection with a simple query
                with self.snowflake_conn.cursor() as cur:
                    cur.execute("SELECT CURRENT_VERSION()")
                    version = cur.fetchone()
                    logger.info(f"Successfully connected to Snowflake version: {version[0]}")
                
                return True
                
            except Exception as e:
                logger.error(f"Failed to establish Snowflake connection: {e}")
                self.snowflake_conn = None
                return False
    
    def get_snowflake_connection(self):
        """
        Get the shared Snowflake connection instance.
        Automatically reconnects if connection is closed.
        
        Returns:
            snowflake.connector.SnowflakeConnection: Active Snowflake connection or None
        """
        with self._connection_lock:
            if self.snowflake_conn is None or self.snowflake_conn.is_closed():
                logger.warning("Snowflake connection lost, attempting to reconnect...")
                if not self.initialize_connections():
                    logger.error("Failed to reconnect to Snowflake")
                    return None
            
            return self.snowflake_conn
    
    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict]:
        """
        Execute a SQL query on Snowflake using the shared connection.
        
        Args:
            query (str): SQL query to execute
            params (tuple, optional): Query parameters
            
        Returns:
            List[Dict]: Query results as list of dictionaries
        """
        logger.debug(f"Executing query: {query[:100]}...")
        
        conn = self.get_snowflake_connection()
        if conn is None:
            logger.warning("No Snowflake connection available. Using mock data for demonstration.")
            return self._get_mock_data(query)
        
        try:
            with conn.cursor(snowflake.connector.DictCursor) as cur:
                cur.execute(query, params)
                results = cur.fetchall()
                logger.debug(f"Query executed successfully, got {len(results)} results")
                return results
                
        except Exception as e:
            logger.error(f"Failed to execute Snowflake query: {e}")
            logger.debug(f"Query: {query}")
            logger.debug(f"Params: {params}")
            raise e

    def call_cortex_llm(self, prompt_text: str, model: str = 'mixtral-8x7b', expect_json: bool = True):
        """
        Call Snowflake Cortex LLM using the shared connection.

        Args:
            prompt_text (str): The prompt to send to the LLM
            model (str): The LLM model to use (default: mixtral-8x7b)
            expect_json (bool): Whether to expect and parse JSON response

        Returns:
            dict/str: The parsed JSON response from the LLM, raw string if expect_json=False, or None if failed
        """
        conn = self.get_snowflake_connection()
        if conn is None:
            logger.error("Cannot call Cortex LLM: No Snowflake connection available")
            return None

        try:
            # Escape single quotes in the prompt
            escaped_prompt = prompt_text.replace("'", "''")

            # Construct the Cortex LLM query
            cortex_query = f"SELECT SNOWFLAKE.CORTEX.COMPLETE('{model}', '{escaped_prompt}') as response"

            logger.debug(f"Calling Cortex LLM with model: {model}")

            with conn.cursor() as cur:
                cur.execute(cortex_query)
                result = cur.fetchone()

                if result and result[0]:
                    response_text = result[0]
                    logger.debug(f"Cortex LLM response received: {response_text[:200]}...")

                    if expect_json:
                        try:
                            # Clean the response text
                            cleaned_response = self._clean_json_response(response_text)
                            parsed_response = json.loads(cleaned_response)
                            return parsed_response
                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to parse JSON response from Cortex LLM: {e}")
                            logger.debug(f"Raw response: {response_text}")
                            return None
                    else:
                        return response_text
                else:
                    logger.error("No response received from Cortex LLM")
                    return None

        except Exception as e:
            logger.error(f"Error calling Cortex LLM: {e}")
            return None

    def _clean_json_response(self, response_text: str) -> str:
        """
        Clean JSON response from LLM by removing markdown formatting and extra text.

        Args:
            response_text (str): Raw response from LLM

        Returns:
            str: Cleaned JSON string
        """
        # Remove markdown code blocks
        response_text = re.sub(r'```json\s*', '', response_text)
        response_text = re.sub(r'```\s*$', '', response_text)

        # Find JSON object boundaries
        json_start = response_text.find('{')
        json_end = response_text.rfind('}') + 1

        if json_start != -1 and json_end > json_start:
            return response_text[json_start:json_end]

        return response_text.strip()

    def close_connections(self):
        """
        Close all connections. Should be called during application shutdown.
        """
        with self._connection_lock:
            if self.snowflake_conn is not None:
                try:
                    self.snowflake_conn.close()
                    logger.info("Snowflake connection closed")
                except Exception as e:
                    logger.error(f"Error closing Snowflake connection: {e}")
                finally:
                    self.snowflake_conn = None

    def is_connected(self) -> bool:
        """
        Check if Snowflake connection is active.

        Returns:
            bool: True if connected, False otherwise
        """
        return (self.snowflake_conn is not None and
                not self.snowflake_conn.is_closed())

    def get_connection_info(self) -> Dict[str, Any]:
        """
        Get information about current connections.

        Returns:
            Dict[str, Any]: Connection status information
        """
        return {
            'snowflake_connected': self.is_connected(),
            'connection_params': {k: v for k, v in (self.connection_params or {}).items()
                                if k not in ['password', 'passcode']},  # Exclude sensitive info
            'initialized': self._initialized
        }

    def _get_mock_data(self, query: str) -> List[Dict]:
        """
        Provide mock data when Snowflake connection is not available.

        Args:
            query (str): SQL query (used to determine what mock data to return)

        Returns:
            List[Dict]: Mock data appropriate for the query
        """
        query_lower = query.lower()

        if 'company_4130_data' in query_lower or 'ticket' in query_lower:
            # Mock ticket data
            return [
                {
                    'TICKETNUMBER': 'T20240101.0001',
                    'ISSUE_TYPE': 'Software/SaaS',
                    'SUB_ISSUE_TYPE': 'Application Error',
                    'TICKET_CATEGORY': 'Technical Support',
                    'PRIORITY': 'Medium',
                    'DESCRIPTION': 'Mock ticket for testing purposes',
                    'STATUS': 'Open',
                    'DUE_DATE': '2024-01-08',
                    'CREATEDATE': '2024-01-01T10:00:00'
                }
            ]
        elif 'technician_dummy_data' in query_lower or 'technician' in query_lower:
            # Mock technician data
            return [
                {
                    'TECHNICIAN_ID': 'TECH-001',
                    'NAME': 'John Smith',
                    'EMAIL': '<EMAIL>',
                    'ROLE': 'Senior Technician',
                    'SKILLS': 'Windows, Network, Hardware',
                    'AVAILABILITY_STATUS': 'Available',
                    'CURRENT_WORKLOAD': '3',
                    'SPECIALIZATIONS': 'Network Infrastructure'
                }
            ]
        else:
            # Generic mock response
            return [{'result': 'mock_data', 'timestamp': datetime.now().isoformat()}]


# Global singleton instance
connection_manager = ConnectionManager()


# Convenience functions for backward compatibility
def get_connection_manager() -> ConnectionManager:
    """Get the global connection manager instance."""
    return connection_manager


def initialize_connections() -> bool:
    """Initialize connections using the global connection manager."""
    return connection_manager.initialize_connections()


def get_snowflake_connection():
    """Get Snowflake connection from the global connection manager."""
    return connection_manager.get_snowflake_connection()


def execute_query(query: str, params: Optional[tuple] = None) -> List[Dict]:
    """Execute query using the global connection manager."""
    return connection_manager.execute_query(query, params)


def call_cortex_llm(prompt_text: str, model: str = 'mixtral-8x7b', expect_json: bool = True):
    """Call Cortex LLM using the global connection manager."""
    return connection_manager.call_cortex_llm(prompt_text, model, expect_json)


def close_connections():
    """Close connections using the global connection manager."""
    return connection_manager.close_connections()
