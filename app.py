"""
TeamLogic-AutoTask Application
Updated to use new login-based UI structure with role-based navigation.
Now uses centralized connection management for Snowflake and Cortex LLM.
"""

import warnings
warnings.filterwarnings("ignore", message="You have an incompatible version of 'pyarrow' installed")

import streamlit as st
import sys
import os

# Add the src directory to the path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import centralized connection manager
from src.database.connection_manager import get_connection_manager, initialize_connections

# Import the new UI pages
from src.ui.login import main as login_main
from src.ui.Pages.User import main as user_main
from src.ui.Pages.Technician import main as technician_main
# from src.ui.Pages.Admin import main as admin_main  # TODO: Create Admin.py


def initialize_app_connections():
    """
    Initialize database connections for Streamlit app.
    This ensures connections are established when the app starts.
    """
    if "connections_initialized" not in st.session_state:
        with st.spinner("Initializing database connections..."):
            success = initialize_connections()
            if success:
                st.session_state.connections_initialized = True
                st.success("✅ Database connections established successfully!")
            else:
                st.session_state.connections_initialized = False
                st.warning("⚠️ Database connection failed - running in mock mode")

        # Display connection info in sidebar for debugging
        from src.ui.components import display_database_status, display_ai_processing_status
        display_database_status()
        display_ai_processing_status()


def main():
    """Main application entry point with new login-based UI structure."""

    # Initialize database connections first
    initialize_app_connections()

    # Initialize session state for role-based navigation
    if "current_role" not in st.session_state:
        st.session_state.current_role = None

    # Route based on selected role
    if st.session_state.current_role == "technician":
        # Run technician UI
        technician_main()
    elif st.session_state.current_role == "user":
        # Run user UI
        user_main()
    elif st.session_state.current_role == "admin":
        # Run admin UI (TODO: Implement Admin.py)
        st.error("❌ Admin dashboard not yet implemented. Please create src/ui/Pages/Admin.py")
        if st.button("🔙 Back to Login"):
            st.session_state.current_role = None
            st.rerun()
    else:
        # Show login page
        login_main()


if __name__ == "__main__":
    main()
