"""
Database connection and operations module for TeamLogic-AutoTask application.
Now uses centralized connection manager for Snowflake connections.
This module provides backward compatibility while using the centralized connection manager.
"""

import pandas as pd
import re
import json
from typing import List, Dict, Optional
import sys
import os

# Add project root to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.database.connection_manager import get_connection_manager


class SnowflakeConnection:
    """
    Backward compatibility wrapper for Snowflake database connections.
    Now delegates to the centralized connection manager.
    """

    def __init__(self, sf_account: str, sf_user: str, sf_password: str,
                 sf_warehouse: str, sf_database: str, sf_schema: str,
                 sf_role: str, sf_passcode: str):
        """
        Initialize Snowflake connection parameters.
        Note: Parameters are ignored as we use the centralized connection manager.

        Args:
            sf_account (str): Snowflake account identifier (ignored)
            sf_user (str): Snowflake username (ignored)
            sf_password (str): Snowflake password (ignored)
            sf_warehouse (str): Snowflake warehouse to use (ignored)
            sf_database (str): Snowflake database to use (ignored)
            sf_schema (str): Snowflake schema to use (ignored)
            sf_role (str): Snowflake role to use (ignored)
            sf_passcode (str): Snowflake MFA passcode (ignored)
        """
        # Store parameters for backward compatibility (though they're not used)
        self.sf_account = sf_account
        self.sf_user = sf_user
        self.sf_password = sf_password
        self.sf_warehouse = sf_warehouse
        self.sf_database = sf_database
        self.sf_schema = sf_schema
        self.sf_role = sf_role
        self.sf_passcode = sf_passcode

        # Get the centralized connection manager
        self.connection_manager = get_connection_manager()

        # Initialize connections if not already done
        if not self.connection_manager.is_connected():
            print("Initializing centralized Snowflake connection...")
            self.connection_manager.initialize_connections()

        print("SnowflakeConnection wrapper initialized with centralized connection manager")

    @property
    def conn(self):
        """
        Get the shared Snowflake connection from the centralized manager.
        Provides backward compatibility for code that accesses self.conn directly.
        """
        return self.connection_manager.get_snowflake_connection()

    def _connect_to_snowflake(self):
        """
        Backward compatibility method - now delegates to connection manager.
        """
        return self.connection_manager.initialize_connections()

    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict]:
        """
        Executes a SQL query on Snowflake using the centralized connection manager.

        Args:
            query (str): The SQL query string
            params (tuple, optional): Parameters to pass to the query

        Returns:
            list: A list of dictionaries, where each dictionary represents a row
        """
        return self.connection_manager.execute_query(query, params)

    def call_cortex_llm(self, prompt_text: str, model: str = 'mixtral-8x7b', expect_json: bool = True):
        """
        Calls the Snowflake Cortex LLM using the centralized connection manager.

        Args:
            prompt_text (str): The prompt to send to the LLM
            model (str): The LLM model to use
            expect_json (bool): Whether to expect and parse JSON response

        Returns:
            dict/str: The parsed JSON response from the LLM, raw string if expect_json=False, or None if failed
        """
        return self.connection_manager.call_cortex_llm(prompt_text, model, expect_json)

    def find_similar_tickets(self, search_conditions: List[str], params: List[str]) -> List[Dict]:
        """
        Searches for similar tickets based on provided conditions.

        Args:
            search_conditions (list): List of SQL WHERE conditions
            params (list): List of parameters for the conditions

        Returns:
            list: List of similar tickets
        """
        where_clause = ""
        if search_conditions:
            where_clause = "WHERE " + " OR ".join(search_conditions)

        query = f"""
        SELECT
            TITLE,
            DESCRIPTION,
            ISSUETYPE,
            SUBISSUETYPE,
            TICKETCATEGORY,
            TICKETTYPE,
            PRIORITY,
            STATUS
        FROM TEST_DB.PUBLIC.COMPANY_4130_DATA
        {where_clause}
        LIMIT 50;
        """
        print(f"Searching for similar tickets...")
        return self.execute_query(query, tuple(params))

    def fetch_reference_tickets(self) -> pd.DataFrame:
        """
        Fetches actual historical tickets with real, detailed resolutions.

        Returns:
            pd.DataFrame: DataFrame containing historical tickets with resolutions
        """
        query = """
            SELECT TITLE, DESCRIPTION, ISSUETYPE, SUBISSUETYPE, PRIORITY, RESOLUTION
            FROM TEST_DB.PUBLIC.COMPANY_4130_DATA
            WHERE RESOLUTION IS NOT NULL
            AND RESOLUTION != ''
            AND RESOLUTION != 'N/A'
            AND RESOLUTION != 'None'
            AND RESOLUTION NOT LIKE '%contact%'
            AND RESOLUTION NOT LIKE '%escalate%'
            AND RESOLUTION NOT LIKE '%call%'
            AND LENGTH(RESOLUTION) > 50
            AND TITLE IS NOT NULL
            AND DESCRIPTION IS NOT NULL
            AND LENGTH(TITLE) > 10
            AND LENGTH(DESCRIPTION) > 20
            ORDER BY LENGTH(RESOLUTION) DESC, RANDOM()
            LIMIT 200
        """
        print("Fetching actual historical tickets with real resolutions...")
        results = self.execute_query(query)

        if results:
            df = pd.DataFrame(results)
            print(f"Fetched {len(df)} historical tickets")

            # Additional filtering for actual technical resolutions
            df = df[df['RESOLUTION'].str.len() > 50]

            # Filter out generic responses
            generic_patterns = [
                'please try', 'contact support', 'escalate to', 'call helpdesk',
                'generic solution', 'standard procedure', 'follow up with'
            ]

            for pattern in generic_patterns:
                df = df[~df['RESOLUTION'].str.contains(pattern, case=False, na=False)]

            # Keep only resolutions with actual technical content
            technical_indicators = [
                'restart', 'configure', 'install', 'update', 'check', 'verify',
                'run', 'execute', 'open', 'close', 'delete', 'create', 'modify',
                'setting', 'option', 'parameter', 'file', 'folder', 'registry',
                'service', 'process', 'application', 'system'
            ]

            technical_mask = df['RESOLUTION'].str.contains('|'.join(technical_indicators), case=False, na=False)
            df = df[technical_mask]

            print(f"After filtering for actual technical resolutions: {len(df)} tickets available")

            return df
        else:
            print("No historical tickets found")
            return pd.DataFrame()

    def close_connection(self):
        """
        Close the Snowflake connection.
        Note: This now delegates to the centralized connection manager.
        """
        print("Note: Connection is managed centrally. Use connection_manager.close_connections() to close all connections.")
        # Don't actually close the connection here as it's shared across the application