from fastapi import FastAPI
import sys
import os

# Add project root to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from .routers import tickets, technicians
from src.database.connection_manager import initialize_connections, close_connections

app = FastAPI(
    title="TeamLogic AutoTask API",
    description="AI-powered IT support ticket management system",
    version="1.0.0"
)

@app.on_event("startup")
async def startup_event():
    """Initialize centralized database connections on startup."""
    success = initialize_connections()
    if success:
        print("✅ Centralized Snowflake connection established successfully")
    else:
        print("⚠️ Failed to establish Snowflake connection - running in mock mode")

@app.on_event("shutdown")
async def shutdown_event():
    """Close all database connections on shutdown."""
    close_connections()
    print("🔌 Database connections closed")

# Include API routers
app.include_router(tickets.router, prefix="/api", tags=["tickets"])
app.include_router(technicians.router, prefix="/api", tags=["technicians"])

@app.get("/")
def read_root():
    """Root endpoint with API information."""
    return {
        "message": "TeamLogic AutoTask FastAPI backend is running!",
        "version": "1.0.0",
        "docs": "/docs",
        "api_prefix": "/api"
    }

@app.get("/health")
def health_check():
    """Health check endpoint."""
    from src.database.connection_manager import get_connection_manager

    conn_manager = get_connection_manager()
    connection_info = conn_manager.get_connection_info()

    return {
        "status": "healthy",
        "database_connected": connection_info["snowflake_connected"],
        "connection_info": connection_info
    }